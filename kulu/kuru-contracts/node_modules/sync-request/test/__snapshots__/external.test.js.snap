// Jest <PERSON>napshot v1, https://goo.gl/fbAQLP

exports[`http://httpbin.org/post 1`] = `
Object {
  "args": Object {},
  "data": "<body/>",
  "files": Object {},
  "form": Object {},
  "headers": Object {
    "Accept-Encoding": "gzip,deflate",
    "Connection": "close",
    "Content-Length": "7",
    "Host": "httpbin.org",
  },
  "json": null,
  "url": "http://httpbin.org/post",
}
`;

exports[`http://httpbin.org/post form 1`] = `
Object {
  "args": Object {},
  "data": "",
  "files": Object {},
  "form": Object {
    "foo": "bar",
  },
  "headers": Object {
    "Accept-Encoding": "gzip,deflate",
    "Connection": "close",
    "Content-Length": "161",
    "Host": "httpbin.org",
  },
  "json": null,
  "url": "http://httpbin.org/post",
}
`;

exports[`http://httpbin.org/post json 1`] = `
Object {
  "args": Object {},
  "data": "{\\"foo\\":\\"bar\\"}",
  "files": Object {},
  "form": Object {},
  "headers": Object {
    "Accept-Encoding": "gzip,deflate",
    "Connection": "close",
    "Content-Length": "13",
    "Content-Type": "application/json",
    "Host": "httpbin.org",
  },
  "json": Object {
    "foo": "bar",
  },
  "url": "http://httpbin.org/post",
}
`;
