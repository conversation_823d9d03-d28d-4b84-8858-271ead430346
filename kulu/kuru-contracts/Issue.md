The call order in deployProxy() is:

verifiedMarket[proxy] = …;

IMarginAccount(marginAccountAddress).updateMarkets(proxy); ⟵ called here

IOrderBook(proxy).initialize(...);

_kuruAmmVault.initialize(...);

That means MarginAccount.updateMarkets(proxy) runs before the OrderBook proxy is initialized.

Why this is a real (valid) issue

An ERC1967 proxy points at live code but its storage is zeroed until initialize runs.

If MarginAccount.updateMarkets reads anything from the market (owner/router, base/quote tokens, decimals, fee params, trusted forwarder, etc.) or performs sanity checks that depend on initialized state, it will:

Revert (availability issue), or

Record zero/garbage (stale config that never gets corrected), or

Whitelist a market that didn’t pass intended checks (e.g., “owner must be Router”), because pre-init owner() might return address(0) instead of Router.

You can’t assume it’s harmless because you don’t control how updateMarkets is implemented. Many accounts modules validate the market’s owner or pull token/decimal config during registration.

Concrete failure examples (common in the wild)

require(market.owner() == router, "not router-owned"); → fails pre-init.

decimalCache[market] = market.baseDecimals(); → stores 0 pre-init.

require(market.isInitialized(), "not ready"); → fails by design.

Even if today’s updateMarkets doesn’t read anything, future upgrades might—making this a latent landmine.

Minimal, safe fix (reorder calls)

Move the registration after both market and vault are initialized: